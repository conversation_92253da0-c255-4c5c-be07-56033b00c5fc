#!/usr/bin/env python3
"""
修复标签编码器 - 从训练数据重新生成并保存标签编码器
"""

import json
import sys
from pathlib import Path
from sklearn.preprocessing import LabelEncoder
import numpy as np

# 直接定义路径，避免导入复杂模块
DATA_ROOT = "/home/<USER>/ZhouSQ/DCX/TACL_math1"
TRAIN_DATA_PATH = f"{DATA_ROOT}/qwen-fintune/data/train.json"
OUTPUT_DIR = Path(__file__).parent / "outputs"

def load_training_data():
    """加载训练数据"""
    print("📊 加载训练数据...")

    with open(TRAIN_DATA_PATH, 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    
    texts = []
    labels = []
    for item in train_data:
        if 'doc_token' in item and 'doc_label' in item:
            texts.append(item['doc_token'])
            labels.append(item['doc_label'])
    
    print(f"✅ 加载了 {len(texts)} 条训练数据")
    return texts, labels

def create_label_encoder(labels):
    """创建标签编码器"""
    print("🏷️  创建标签编码器...")
    
    # 将层次标签转换为字符串
    label_strings = [" -> ".join(label) for label in labels]
    
    # 创建标签编码器
    label_encoder = LabelEncoder()
    label_encoder.fit(label_strings)
    
    print(f"✅ 创建了 {len(label_encoder.classes_)} 个唯一标签")
    
    # 显示前几个标签作为示例
    print("📋 标签示例:")
    for i, label in enumerate(label_encoder.classes_[:5]):
        print(f"  {i}: {label}")
    
    return label_encoder

def save_label_encoder(label_encoder, model_path):
    """保存标签编码器到模型目录"""
    print(f"💾 保存标签编码器到: {model_path}")
    
    label_encoder_path = Path(model_path) / "label_encoder.json"
    
    label_mapping = {
        'classes_': label_encoder.classes_.tolist()
    }
    
    with open(label_encoder_path, 'w', encoding='utf-8') as f:
        json.dump(label_mapping, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 标签编码器已保存: {label_encoder_path}")

def verify_label_encoder(model_path):
    """验证标签编码器"""
    print("🔍 验证标签编码器...")
    
    label_encoder_path = Path(model_path) / "label_encoder.json"
    
    if not label_encoder_path.exists():
        print("❌ 标签编码器文件不存在")
        return False
    
    try:
        with open(label_encoder_path, 'r', encoding='utf-8') as f:
            label_mapping = json.load(f)
        
        num_labels = len(label_mapping.get('classes_', []))
        print(f"✅ 标签编码器验证成功，包含 {num_labels} 个标签")
        
        # 显示一些统计信息
        classes = label_mapping['classes_']
        print(f"📊 标签统计:")
        print(f"  总数: {len(classes)}")
        print(f"  示例: {classes[:3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 修复标签编码器")
    print("=" * 60)
    
    try:
        # 1. 加载训练数据
        texts, labels = load_training_data()
        
        # 2. 创建标签编码器
        label_encoder = create_label_encoder(labels)
        
        # 3. 找到模型目录
        model_path = OUTPUT_DIR / "models" / "checkpoint-42189"
        if not model_path.exists():
            print(f"❌ 模型目录不存在: {model_path}")
            return False
        
        print(f"📁 模型目录: {model_path}")
        
        # 4. 保存标签编码器
        save_label_encoder(label_encoder, model_path)
        
        # 5. 验证标签编码器
        if verify_label_encoder(model_path):
            print("\n🎉 标签编码器修复完成！")
            print("\n💡 现在可以重新测试模型:")
            print("  python main.py predict --query '你的问题'")
            print("  python main.py evaluate")
            return True
        else:
            print("\n❌ 标签编码器验证失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 修复过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
